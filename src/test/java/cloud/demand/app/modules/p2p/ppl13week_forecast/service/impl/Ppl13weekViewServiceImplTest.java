package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_CBS;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_EKS;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_EMR;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.CreateSplitVersionReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekViewService;
import com.google.common.collect.ImmutableMap;
import java.time.LocalDateTime;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class Ppl13weekViewServiceImplTest {

    @Resource
    Ppl13weekViewService ppl13weekViewService;

    @Test
    void createSplitVersion() {
        CreateSplitVersionReq req = new CreateSplitVersionReq();
        req.setOutputVersionName("测试拆分-" + LocalDateTime.now());
        req.setOutputVersionType(SPLIT_EMR.getCode());
        req.setTaskIds(Lang.list(6082L));

        ImmutableMap<String, String> splitVersion = ppl13weekViewService.createSplitVersion(req);

        for (String value : splitVersion.values()) {
            System.out.println(value);
        }
    }
    @Test
    void createSplitEksVersion() {
        CreateSplitVersionReq req = new CreateSplitVersionReq();
        req.setOutputVersionName("测试拆分-" + LocalDateTime.now());
        req.setOutputVersionType(SPLIT_EKS.getCode());
        req.setTaskIds(Lang.list(6143L));
        ImmutableMap<String, String> splitVersion = ppl13weekViewService.createSplitVersion(req);
        for (String value : splitVersion.values()) {
            System.out.println(value);
        }
    }

    @Test
    void createSplitCDBVersion() {
        CreateSplitVersionReq req = new CreateSplitVersionReq();
        req.setOutputVersionName("测试拆分-" + LocalDateTime.now());
        req.setOutputVersionType(SPLIT_CBS.getCode());
        req.setTaskIds(Lang.list(6340L));
        ImmutableMap<String, String> splitVersion = ppl13weekViewService.createSplitVersion(req);
        for (String value : splitVersion.values()) {
            System.out.println(value);
        }
    }



}