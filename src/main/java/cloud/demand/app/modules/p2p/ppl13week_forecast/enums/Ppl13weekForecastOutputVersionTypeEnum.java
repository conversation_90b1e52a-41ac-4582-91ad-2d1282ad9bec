package cloud.demand.app.modules.p2p.ppl13week_forecast.enums;

import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum Ppl13weekForecastOutputVersionTypeEnum {

    /**
     * 系统版本
     */
    SYSTEM("SYSTEM", "系统版本"),

    /**
     * 13周下发版本
     */
    NEW_SPLIT_FOR_PPL("NEW_SPLIT_FOR_PPL", "PPL13周新中长尾下发拆分"),

    /**
     * EMR的拆分
     */
    SPLIT_EMR("SPLIT_EMR", "拆分EMR"),

    // CBS的拆分
    SPLIT_CBS("SPLIT_CBS", "拆分CBS"),

    /**
     * EMR的拆分
     */
    SPLIT_EKS("SPLIT_EKS", "拆分EKS"),

    /**
     * EMR的拆分
     */
    SPLIT_CDB("SPLIT_CDB", "拆分CDB"),
    /**
     * ZIYAN
     */
    SPLIT_FOR_ZIYAN("SPLIT_FOR_ZIYAN", "自研需求拆分"),

    /**
     *
     * 这个其实是头部里面的数据,在一键替换的时候会使用到，腰部一已经没有使用了
     * 腰部需求拆分
     */
    SPLIT_FOR_MIDDLE("SPLIT_FOR_MIDDLE", "腰部需求拆分"),
    ;

    final private String code;
    final private String name;

    Ppl13weekForecastOutputVersionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Ppl13weekForecastOutputVersionTypeEnum getByCode(String code) {
        for (Ppl13weekForecastOutputVersionTypeEnum e : Ppl13weekForecastOutputVersionTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        Ppl13weekForecastOutputVersionTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}