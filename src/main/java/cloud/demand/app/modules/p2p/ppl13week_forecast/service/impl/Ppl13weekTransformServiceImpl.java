package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.NEW_SPLIT_FOR_PPL;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_CBS;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_EMR;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ListUtils2;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoLatestWithoutJsonDO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ApproveVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupApproveResultEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.ForecastKey;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.ForecastKey.Key;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigHolidayWeekDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigSpikeThresholdDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultForZiyanSplitDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultSplitDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskOutputVersionDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastTransformRecord;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastTransformToPplDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.YuntiDemandCvmItemForecastDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.ConfigWhiteList;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTransformStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekTransformReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekTransformStatusRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.vo.PplForecastPredictResultSplitWithTaskVO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastBillTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCustomerScopeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekPredictService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekTransformService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13weekPredictServiceImpl.InstanceTypeInfo;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

/**
 * <AUTHOR>
 */
@SuppressWarnings("SpringTransactionalComponentInspection")
@Service
@Slf4j
public class Ppl13weekTransformServiceImpl implements Ppl13weekTransformService {


    @Resource
    Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;
    @Resource
    PplVersionGroupService pplVersionGroupService;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    DictService dictService;

    @Resource
    private Ppl13weekPredictService ppl13weekPredictService;

    private static final List<String> pplOrderPool = new ArrayList<>();

    /**
     * 排除下发ppl的机型
     */
    private static final Supplier<String> excludeInstanceType =
            DynamicProperty.create("forecast.exclude_to_ppl.instance_type", "S5,M5,SA2,DA4");

    @Override
    public QueryPpl13weekTransformStatusRsp queryTransFormStatus(QueryPpl13weekTransformReq req) {
        PplForecastTransformRecord one = DBList.demandDBHelper
                .getOne(PplForecastTransformRecord.class, "where output_version_id in (?)", req.getOutputVersionIds());
        if (one == null) {
            return new QueryPpl13weekTransformStatusRsp();
        }
        return transTo(one);
    }

    private QueryPpl13weekTransformStatusRsp transTo(PplForecastTransformRecord one) {
        QueryPpl13weekTransformStatusRsp queryPpl13weekTransformStatusRsp = new QueryPpl13weekTransformStatusRsp();
        queryPpl13weekTransformStatusRsp.setCreateTime(one.getCreateTime());
        queryPpl13weekTransformStatusRsp.setDesc(one.getDesc());
        queryPpl13weekTransformStatusRsp.setStatus(one.getStatus());
        queryPpl13weekTransformStatusRsp.setStatusName(PplForecastTransformStatusEnum.getNameByCode(one.getStatus()));
        return queryPpl13weekTransformStatusRsp;
    }


    public ConfigWhiteList getConfigWhiteList() {
        String sql = "SELECT type, name FROM cloud_demand.ppl_forecast_config_white_list_to_ppl";
        List<String> azList = new ArrayList<>();
        List<String> modelList = new ArrayList<>();
        List<String> regionList = new ArrayList<>();

        DBList.getJdbcTemplate(DBList.demandDBHelper).query(sql, rs -> {
            String type = rs.getString("type");
            String name = rs.getString("name");
            if ("可用区".equals(type)) {
                azList.add(name);
            } else if ("机型".equals(type)) {
                modelList.add(name);
            } else if ("地域".equals(type)) {
                regionList.add(name);
            }
        });
        return new ConfigWhiteList(azList, modelList, regionList);
    }

    @Override
    public List<YuntiDemandCvmItemForecastDO> getSplitResultByDate(LocalDate date) {
        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/transform/get_max_output_version_id.sql");
        PplForecastPredictTaskOutputVersionDO outputDO = DBList.demandDBHelper.getRawOne(
                PplForecastPredictTaskOutputVersionDO.class, sql, date.getYear(), date.getMonthValue());
        if (outputDO == null) {
            return Lang.list();
        }
        return DBList.demandDBHelper.getAll(
                        PplForecastPredictResultForZiyanSplitDO.class,
                        "where output_version_id=?", outputDO.getId())
                .stream().filter(vo -> vo.getCoreNum() != null && vo.getCoreNum().compareTo(BigDecimal.ZERO) > 0)
                .map(this::trans)
                .parallel().map(this::splitToResPlanMonth)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }


    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public ImmutableMap<String, String> createZiyanTransForm(QueryPpl13weekTransformReq req) {

        if (Lang.isEmpty(req.getOutputVersionIds())) {
            throw BizException.makeThrow("outputVersionId is null");
        }

        List<PplForecastPredictTaskOutputVersionDO> allPredict = DBList.demandDBHelper.getAll(
                PplForecastPredictTaskOutputVersionDO.class,
                "where id in (?)", req.getOutputVersionIds());

        List<YuntiDemandCvmItemForecastDO> data2insert = Lang.list();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (PplForecastPredictTaskOutputVersionDO outputVersionDO : allPredict) {

            PplForecastTransformRecord pplforecasttransformrecord = new PplForecastTransformRecord();
            pplforecasttransformrecord.setTaskId(outputVersionDO.getTaskId());
            pplforecasttransformrecord.setOutputVersionId(outputVersionDO.getId());
            setRecord(req, uuid, pplforecasttransformrecord);
            Long outputVersionId = outputVersionDO.getId();

            List<PplForecastPredictResultForZiyanSplitDO> allDetail = DBList.demandDBHelper.getAll(
                    PplForecastPredictResultForZiyanSplitDO.class, "where output_version_id=?", outputVersionId);
            allDetail = filterDateAndZero(req, pplforecasttransformrecord, allDetail);

            DBList.demandDBHelper.insert(pplforecasttransformrecord);
            for (PplForecastPredictResultForZiyanSplitDO onePredict : allDetail) {
                data2insert.add(trans(onePredict));
            }
        }

        // 插入前，首先处理覆盖删除
        if (!req.getIsAppend()) {

            YearMonth minBeginBuyDate;
            YearMonth maxBeginBuyDate;
            if (!data2insert.isEmpty()) {
                Optional<YuntiDemandCvmItemForecastDO> min = data2insert.stream()
                        .min(Comparator.comparingInt(YuntiDemandCvmItemForecastDO::getYear)
                                .thenComparingInt(YuntiDemandCvmItemForecastDO::getMonth));
                minBeginBuyDate = min.map(o -> YearMonth.of(o.getYear(), o.getMonth())).orElse(null);

                Optional<YuntiDemandCvmItemForecastDO> max = data2insert.stream()
                        .max(Comparator.comparingInt(YuntiDemandCvmItemForecastDO::getYear)
                                .thenComparingInt(YuntiDemandCvmItemForecastDO::getMonth));
                maxBeginBuyDate = max.map(o -> YearMonth.of(o.getYear(), o.getMonth())).orElse(null);
            } else {
                // 这里做一个特殊逻辑，如果下发的没有数据，就清空选中的数据
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                minBeginBuyDate = YearMonth.parse(req.getStartYearMonth(), formatter);
                maxBeginBuyDate = YearMonth.parse(req.getEndYearMonth(), formatter);
            }

            if (minBeginBuyDate != null && maxBeginBuyDate != null) {
                String sql = "WHERE (year > ? OR (year = ? AND month >= ?)) AND (year < ? OR (year = ? AND month <= ?))";
                Object[] params = new Object[]{
                        minBeginBuyDate.getYear(), minBeginBuyDate.getYear(), minBeginBuyDate.getMonthValue(),
                        maxBeginBuyDate.getYear(), maxBeginBuyDate.getYear(), maxBeginBuyDate.getMonthValue()
                };
                DBList.yuntidemandDBHelper.delete(YuntiDemandCvmItemForecastDO.class, sql, params);
            }
        }
        // 这里拆分一下，holidayYear, holidayMonth
        data2insert = data2insert.parallelStream()
                .map(this::splitToResPlanMonth)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        DBList.yuntidemandDBHelper.insert(data2insert);
        return ImmutableMap.of("data", "success");
    }

    /**
     * 拆分成Plan 的周
     *
     * @param source source
     * @return list
     */
    public List<YuntiDemandCvmItemForecastDO> splitToResPlanMonth(YuntiDemandCvmItemForecastDO source) {

        LocalDate firstDay = LocalDate.of(source.getYear(), source.getMonth(), 1);
        // 统计年月的天数
        Map<YearMonth, Long> groupCount = IntStream.range(1, firstDay.lengthOfMonth() + 1)
                .mapToObj((o) -> {
                    ResPlanHolidayWeekDO holiday =
                            dictService.getHolidayWeekInfoByDate(firstDay.withDayOfMonth(o).toString());
                    return YearMonth.of(holiday.getYear(), holiday.getMonth());
                }).collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        int lengthOfMonth = firstDay.lengthOfMonth();
        return groupCount.keySet().stream().map((o) -> {
            YuntiDemandCvmItemForecastDO clone = JSON.clone(source);
            BigDecimal rate = BigDecimal.valueOf(groupCount.get(o))
                    .divide(BigDecimal.valueOf(lengthOfMonth), 5, RoundingMode.HALF_UP);
            clone.setCoreNum(source.getCoreNum().multiply(rate));
            clone.setHolidayYear(o.getYear());
            clone.setHolidayMonth(o.getMonthValue());
            return clone;
        }).collect(Collectors.toList());
    }

    private List<PplForecastPredictResultForZiyanSplitDO> filterDateAndZero(
            QueryPpl13weekTransformReq req, PplForecastTransformRecord pplforecasttransformrecord,
            List<PplForecastPredictResultForZiyanSplitDO> allDetail) {
        // 过滤掉条件不符合的数据
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate startDate = YearMonth.parse(req.getStartYearMonth(), formatter).atDay(1);
        LocalDate endDate = YearMonth.parse(req.getEndYearMonth(), formatter).atEndOfMonth();
        // 过滤记录，并统计不同条件过滤掉的记录数
        int total = allDetail.size();
        AtomicInteger zeroFiltered = new AtomicInteger();
        AtomicInteger dateFiltered = new AtomicInteger();
        allDetail = allDetail.stream()
                .filter(vo -> {
                    boolean isValid = vo.getCoreNum() != null && vo.getCoreNum().compareTo(BigDecimal.ZERO) > 0;
                    if (!isValid) {
                        zeroFiltered.getAndIncrement();
                    }
                    return isValid;
                })
                .filter(vo -> {
                    // 判断时间范围是否包含记录的时间
                    LocalDate recordDate = vo.getStatTime();
                    boolean isValid = (recordDate.isEqual(startDate) || recordDate.isAfter(startDate))
                            && (recordDate.isEqual(endDate) || recordDate.isBefore(endDate));
                    if (!isValid) {
                        dateFiltered.getAndIncrement();
                    }
                    return isValid;
                })
                .collect(Collectors.toList());

        String desc = String.format("共查询到%d条记录，其中cvm=0的过滤掉%d条记录，时间范围过滤掉%d条记录,剩余%d条记录。",
                total, zeroFiltered.get(), dateFiltered.get(), allDetail.size());
        pplforecasttransformrecord.setDesc(desc);
        return allDetail;
    }

    private YuntiDemandCvmItemForecastDO trans(PplForecastPredictResultForZiyanSplitDO o) {
        YuntiDemandCvmItemForecastDO yuntiDemandCvmItemForecastDO = new YuntiDemandCvmItemForecastDO();
        yuntiDemandCvmItemForecastDO.setSplitId(o.getId());
        yuntiDemandCvmItemForecastDO.setOutputVersionId(o.getOutputVersionId());
        yuntiDemandCvmItemForecastDO.setYear(o.getYear());
        yuntiDemandCvmItemForecastDO.setMonth(o.getMonth());
        yuntiDemandCvmItemForecastDO.setRegionName(o.getRegionName());
        yuntiDemandCvmItemForecastDO.setCustomhouseTitle(o.getCustomhouseTitle());
        yuntiDemandCvmItemForecastDO.setDeviceGroup(o.getDeviceGroup());
        yuntiDemandCvmItemForecastDO.setGinsFamily(o.getGinsFamily());
        yuntiDemandCvmItemForecastDO.setProjectName(o.getProjectName());
        yuntiDemandCvmItemForecastDO.setCustomBgName(o.getCustomBgName());
        yuntiDemandCvmItemForecastDO.setCoreNum(o.getCoreNum());
        return yuntiDemandCvmItemForecastDO;
    }

    /**
     * 下发到 crp
     *
     * @param req req
     * @return ret success info
     */
    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public ImmutableMap<String, String> createTransForm(QueryPpl13weekTransformReq req) {

        if (Lang.isEmpty(req.getOutputVersionIds())) {
            throw BizException.makeThrow("outputVersionId is null");
        }
        // req.getOutputVersionIds() 这个之前是多条的，现在改成单条了，因此兼容一下
        PplVersionData pplInfo = loadDataFromDb(req);
        //  CREATE 空item， COMD_INTERVENE 下发到item
        // 2023-10-07 限制一下当前状态的下发
        String groupRecordStatus = pplInfo.versionGroupRecordDO.getStatus();
        if (Lang.list(PplVersionGroupStatusEnum.COMD_APPROVE.getCode(),
                PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode(),
                PplVersionGroupStatusEnum.DONE.getCode()).contains(groupRecordStatus)) {
            throw BizException.makeThrow("当前状态不可以下发ppl。 当前状态：%s",
                    PplVersionGroupStatusEnum.getNameByCode(groupRecordStatus));
        }
        // 这里先把ppl 和要下发的数据存储起来 的数据存储起来
        PplForecastPredictTaskOutputVersionDO outputVersion = pplInfo.getSplitVersions().get(0);
        String outputVersionType = outputVersion.getOutputVersionType();
        // EKS/EMR 不通过
        // 通过一下当前的为 PRE_SUBMIT 的状态
        pplInfo.versionGroupRecordDO = approveIfVersionInStartStatus(pplInfo);

        if (NEW_SPLIT_FOR_PPL.getCode().equals(outputVersionType)) {
            tranCvmForecastToPplVersionItem(req, pplInfo);
        } else if (SPLIT_CBS.getCode().equals(outputVersionType)) {
            tranCbsForecastToPplVersionItem(req, pplInfo);
        } else {
            throw BizException.makeThrow("当前类型不可以下发ppl。 当前类型：%s", outputVersionType);
        }

        return ImmutableMap.of("data", "success");
    }

    private void tranCbsForecastToPplVersionItem(QueryPpl13weekTransformReq req, PplVersionData pplInfo) {
        // 创建记录表
        InsertResult data2insert = getInsertResult();

        if (pplInfo.getSplitVersions().size() > 1) {
            throw BizException.makeThrow("bug: cbs ppl 不能同时下发多个版本");
        }

        PplForecastTransformRecord transformRecord = new PplForecastTransformRecord();
        PplForecastPredictTaskOutputVersionDO outputVersionDO = pplInfo.getSplitVersions().get(0);
        transformRecord.setTaskId(outputVersionDO.getTaskId());
        transformRecord.setOutputVersionId(outputVersionDO.getId());

        String uuid = UUID.randomUUID().toString().replace("-", "");
        setRecord(req, uuid, transformRecord);

        List<PplForecastPredictResultSplitWithTaskVO> allSplitData = getPredictDataBySplitId(outputVersionDO.getId());
        allSplitData = filterDateByReq(req, allSplitData);

        for (PplForecastPredictResultSplitWithTaskVO predict : allSplitData) {
            transCbs2ppl(predict, transformRecord, pplInfo, data2insert);
        }

        PplForecastPredictTaskDO taskDO = allSplitData.get(0).getTaskDO();
        List<String> customerShortNames = Lang.list(taskDO.getSourceType());
        customerShortNames = customerShortNames.stream()
                .map(Ppl13weekForecastSourceTypeEnum::getVersionItemCustomerShortName).collect(Collectors.toList());
        deleteOldDataBeforeInsert(req, data2insert.batchInsertItem2insert, pplInfo, customerShortNames);
        DBList.demandDBHelper.insertBatchWithoutReturnId(data2insert.orders2insert);
        DBList.demandDBHelper.insertBatchWithoutReturnId(data2insert.batchInsertItem2insert);

        DBList.demandDBHelper.insert(transformRecord);
    }

    private static  List<PplForecastPredictResultSplitWithTaskVO> getPredictDataBySplitId( Long outputVersionId) {
        List<PplForecastPredictResultSplitWithTaskVO> allSplitData = DBList.demandDBHelper.getAll(
                PplForecastPredictResultSplitWithTaskVO.class, "where output_version_id=?", outputVersionId);
        if (allSplitData.isEmpty()) {
            throw BizException.makeThrow("没有预测数据");
        }
        return allSplitData;
    }

    private static @NotNull List<PplForecastPredictResultSplitWithTaskVO> filterDateByReq(
            QueryPpl13weekTransformReq req,
            List<PplForecastPredictResultSplitWithTaskVO> allSplitData) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate startDate = YearMonth.parse(req.getStartYearMonth(), formatter).atDay(1);
        LocalDate endDate = YearMonth.parse(req.getEndYearMonth(), formatter).atEndOfMonth();
        // 这里只有过滤数据，日期和零的数据
        allSplitData = allSplitData.stream()
                .filter(vo -> {
                    // 判断时间范围是否包含记录的时间
                    LocalDate recordDate = vo.getStatTime();
                    return (recordDate.isEqual(startDate) || recordDate.isAfter(startDate))
                            && (recordDate.isEqual(endDate) || recordDate.isBefore(endDate));
                })
                .collect(Collectors.toList());
        return allSplitData;
    }


    private void tranCvmForecastToPplVersionItem(QueryPpl13weekTransformReq req, PplVersionData transToPplVersionData) {
        // 创建记录表
        InsertResult data2insert = getInsertResult();
        // 用于覆盖下发
        List<String> customerShortNames = Lang.list();

        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (PplForecastPredictTaskOutputVersionDO outputVersionDO : transToPplVersionData.splitVersions) {
            PplForecastTransformRecord transformRecord = new PplForecastTransformRecord();
            transformRecord.setTaskId(outputVersionDO.getTaskId());
            transformRecord.setOutputVersionId(outputVersionDO.getId());
            setRecord(req, uuid, transformRecord);

            Long outputVersionId = outputVersionDO.getId();
            List<PplForecastPredictResultSplitWithTaskVO> allSplitData = DBList.demandDBHelper.getAll(
                    PplForecastPredictResultSplitWithTaskVO.class, "where output_version_id=?", outputVersionId);

            // 排除不需要下发的机型
            String excludeInstanceTypes = excludeInstanceType.get();
            if (StringTools.isNotBlank(excludeInstanceTypes)) {
                String[] excludeInstanceTypeArr = excludeInstanceTypes.split(",");
                List<String> excludeInstanceTypeList = ListUtils.transform(excludeInstanceTypeArr, o -> o);
                allSplitData = ListUtils.filter(allSplitData,
                        o -> !excludeInstanceTypeList.contains(o.getGinsFamily()));
            }

            if (allSplitData.isEmpty()) {
                throw BizException.makeThrow("没有预测数据");
            }
            PplForecastPredictTaskDO taskDO = allSplitData.get(0).getTaskDO();
            if (!customerShortNames.contains(taskDO.getSourceType())) {
                customerShortNames.add(taskDO.getSourceType());
            }

            boolean isNewLongTail =
                    Objects.equals(taskDO.getCustomerScope(), Ppl13weekForecastCustomerScopeEnum.ALL.getCode());
            if (!isNewLongTail) {
                // 2025-04-14 下面代码已经删除，未使用
                // 这里还加上了聚合数据,并且处理成净增了
                allSplitData = filterAndRecord(req, transformRecord, allSplitData);
                // 获取全量的PPL内外部数据
                for (PplForecastPredictResultSplitWithTaskVO onePredict : allSplitData) {
                    trans2ppl(onePredict, transformRecord, transToPplVersionData, data2insert, null);
                }
            } else {
                // 这里只有过滤数据，日期和零的数据
                allSplitData = newTailFilterAndRecord(req, transformRecord, allSplitData);
                // ppl 模型预测取大
                getMax(transToPplVersionData, taskDO, allSplitData, uuid, transformRecord, data2insert);
            }
            DBList.demandDBHelper.insert(transformRecord);
        }

        customerShortNames = customerShortNames.stream()
                .map(Ppl13weekForecastSourceTypeEnum::getVersionItemCustomerShortName).collect(Collectors.toList());
        deleteOldDataBeforeInsert(req, data2insert.batchInsertItem2insert, transToPplVersionData, customerShortNames);
        // 这里统一插入
        DBList.demandDBHelper.insertBatchWithoutReturnId(data2insert.orders2insert);
        DBList.demandDBHelper.insertBatchWithoutReturnId(data2insert.batchInsertItem2insert);
    }


    private static final Supplier<String> emrBlackInstanceType =
            DynamicProperty.create("forecast.emr-black-instance-type", "BC1;BMD3c;BMI5");


    private @NotNull PplVersionData loadDataFromDb(QueryPpl13weekTransformReq req) {
        // 异步获取 all
        CompletableFuture<List<PplForecastPredictTaskOutputVersionDO>> futureAll = CompletableFuture.supplyAsync(() ->
                DBList.demandDBHelper.getAll(PplForecastPredictTaskOutputVersionDO.class,
                        "where id in (?)", req.getOutputVersionIds())
        );
        // 异步获取 versionDO
        CompletableFuture<PplVersionDO> futureVersionDO = CompletableFuture.supplyAsync(() -> {
                    // 先查找version
                    PplVersionDO versionDO = DBList.demandDBHelper.getOne(PplVersionDO.class, "where version_code=?",
                            req.getVersionCode());
                    if (versionDO == null) {
                        throw BizException.makeThrow("版本不存在");
                    }
                    return versionDO;
                }
        );

        List<PplForecastPredictTaskOutputVersionDO> allSplitVersion = futureAll.join();
        List<String> outputVersionType = allSplitVersion.stream()
                .map(PplForecastPredictTaskOutputVersionDO::getOutputVersionType).distinct()
                .collect(Collectors.toList());

        if (outputVersionType.size() != 1) {
            throw BizException.makeThrow("拆分数据版本出现了多条");
        }

        // CVM CBS 都是CVM product 中
        String versionGroupProduct = Ppl13weekProductTypeEnum.CVM.getName();
        if (SPLIT_EMR.getCode().equals(outputVersionType.get(0))) {
            versionGroupProduct = Ppl13weekProductTypeEnum.EMR.getName();
        }

        // 获取 versionGroupDO, 注意这里不在一个事务，下面抛出异常了，插入的数据不会删除
        PplVersionDO versionDO = futureVersionDO.join();
        PplVersionGroupDO versionGroupDO = getVersionGroup(req, versionGroupProduct);
        // 异步获取 versionGroupRecordDO, 注意这里不在一个事务，下面抛出异常了，插入的数据不会删除
        PplVersionGroupRecordDO versionGroupRecordDO = getVersionGroupRecord(versionGroupDO);

        // 等待所有 CompletableFuture 完成
        return new PplVersionData(allSplitVersion, versionDO, versionGroupDO, versionGroupRecordDO);
    }

    @Data
    private static class PplVersionData {

        public List<PplForecastPredictTaskOutputVersionDO> splitVersions;
        public PplVersionDO versionDO;
        public PplVersionGroupDO versionGroupDO;
        public PplVersionGroupRecordDO versionGroupRecordDO;

        public PplVersionData(List<PplForecastPredictTaskOutputVersionDO> all,
                PplVersionDO versionDO,
                PplVersionGroupDO versionGroupDO,
                PplVersionGroupRecordDO versionGroupRecordDO) {
            this.splitVersions = all;
            this.versionGroupDO = versionGroupDO;
            this.versionDO = versionDO;
            this.versionGroupRecordDO = versionGroupRecordDO;
        }
    }

    private void getMax(PplVersionData pplRecord, PplForecastPredictTaskDO taskDO,
            List<PplForecastPredictResultSplitWithTaskVO> allDetail,
            String uuid, PplForecastTransformRecord transformRecord, InsertResult data2insert) {

        PplVersionGroupDO versionGroupDO = pplRecord.versionGroupDO;

        List<GroupItemDTO> allPpl = getAndFilterPpl(versionGroupDO, taskDO);
        Map<String, String> instanceTypeToGroup = getTransInstanceTypeConfig();
        List<PplForecastTransformToPplDO> toPplRecords = new ArrayList<>();

        ListUtils2.groupAndApply(
                // 模型预测本来就有机型收敛的了
                allDetail, ForecastKey::createKey,
                // ppl 聚合增加了机型收敛
                allPpl, (o) -> {
                    Key key = ForecastKey.createKey(o);
                    String ginsFamily = key.getGinsFamily();
                    String newGinsFamily = instanceTypeToGroup.getOrDefault(ginsFamily, ginsFamily);
                    key.setGinsFamily(newGinsFamily);
                    return key;
                },
                (key, predict, ppl) -> {
                    if (predict == null) {
                        return;
                    }
                    ppl = Optional.ofNullable(ppl).orElse(Collections.emptyList());

                    PplForecastTransformToPplDO toPplRecord
                            = PplForecastTransformToPplDO.transFrom(predict, ppl, uuid);
                    toPplRecords.add(toPplRecord);
                    BigDecimal splitSum = NumberUtils.sum(predict, PplForecastPredictResultSplitDO::getCoreNum);
                    BigDecimal pplSum = NumberUtils.sum(ppl, GroupItemDTO::getTotalCoreNum);
                    toPplRecord.setPredictNum(splitSum);
                    toPplRecord.setPplNum(pplSum);

                    // 模型预测的大， 补齐对应的量
                    if (splitSum.compareTo(pplSum) >= 0) {
                        // 模型预测减去ppl 录入的量
                        BigDecimal totalNeedSubNum = splitSum.subtract(pplSum);
                        for (PplForecastPredictResultSplitWithTaskVO one : predict) {
                            BigDecimal rate = one.getCoreNum().divide(splitSum, 5, RoundingMode.HALF_UP);
                            BigDecimal needSubNum = totalNeedSubNum.multiply(rate);
                            one.setCoreNum(needSubNum);
                            trans2ppl(one, transformRecord, pplRecord, data2insert, toPplRecord);
                        }
                    } else {
                        toPplRecord.setTransformNum(BigDecimal.ZERO); // 模型的量小，这部分量直接不要了
                    }
                });
        DBList.demandDBHelper.insertBatchWithoutReturnId(toPplRecords);
    }

    @NotNull
    private static Map<String, String> getTransInstanceTypeConfig() {
        List<PplForecastConfigSpikeThresholdDO> thresholds = DBList.demandDBHelper.getAll(
                PplForecastConfigSpikeThresholdDO.class, "where threshold > 0");
        Map<String, String> instanceTypeToGroup = new HashMap<>(); // 机型 -> 机型收敛
        for (PplForecastConfigSpikeThresholdDO threshold : thresholds) {
            List<String> splitInstanceTypes = threshold.getSplitInstanceTypes();
            for (String type : splitInstanceTypes) {
                instanceTypeToGroup.put(type, threshold.getCommonInstanceType());
            }
        }
        return instanceTypeToGroup;
    }

    private List<GroupItemDTO> getAndFilterPpl(PplVersionGroupDO forecastGroupDO,
            PplForecastPredictTaskDO taskDO) {
        String versionCode = forecastGroupDO.getVersionCode();
        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/transform/get_ppl_group_by_version.sql");

        SecurityContext context = SecurityContextHolder.getContext();
        List<PplVersionGroupDO> groups = DBList.demandDBHelper.getRaw(PplVersionGroupDO.class, sql, versionCode);
        // 通过名称去判断内外部
        List<IndustryReportAppidInfoLatestWithoutJsonDO> innerCustomer = pplCommonService.getInnerCustomer();
        return groups.parallelStream()
                .flatMap(group -> {
                    SecurityContext context1 = SecurityContextHolder.getContext();
                    SecurityContextHolder.setContext(context);
                    Stream<GroupItemDTO> ret = pplVersionGroupService.queryVersionGroupItem(
                                    group.getId())
                            .getPplItems()
                            .stream()
                            .filter((o) -> Objects.equals(o.getMeddleType(), "未干预")) // 干预前
                            .filter((o) -> !Strings.equals(o.getRegionName(), "随机地域"))
                            .filter((o) -> Strings.isNotBlank(o.getRegionName()))
                            .filter((o) -> Strings.isNotBlank(o.getInstanceType()))
                            .filter((o) -> o.getIsSpikeBeforeComd() == 0)  //常规项目的数据
                            .filter((o) -> { // 内外部过滤
                                boolean isInner = ListUtils.contains(innerCustomer, (inner) ->
                                        Objects.equals(inner.getCustomerShortName(), o.getCustomerShortName()) ||
                                                Objects.equals(String.valueOf(inner.getUin()), o.getCustomerUin()));
                                if (Objects.equals(taskDO.getSourceType(),
                                        Ppl13weekForecastSourceTypeEnum.INDUSTRY.getCode())) {
                                    return !isInner;
                                } else if (Objects.equals(taskDO.getSourceType(),
                                        Ppl13weekForecastSourceTypeEnum.INNER.getCode())) {
                                    return isInner;
                                } else {
                                    return true;
                                }
                            });
                    SecurityContextHolder.setContext(context1);
                    return ret;
                })
                .collect(Collectors.toList());

    }

    @NotNull
    private static InsertResult getInsertResult() {
        List<PplOrderDO> orders2insert = Lang.list();
        List<PplVersionGroupRecordItemDO> batchInsertItem2insert = Lang.list();
        return new InsertResult(orders2insert, batchInsertItem2insert);
    }

    private static class InsertResult {

        public final List<PplOrderDO> orders2insert;
        public final List<PplVersionGroupRecordItemDO> batchInsertItem2insert;

        public InsertResult(List<PplOrderDO> orders2insert, List<PplVersionGroupRecordItemDO> batchInsertItem2insert) {
            this.orders2insert = orders2insert;
            this.batchInsertItem2insert = batchInsertItem2insert;
        }
    }


    private void transCbs2ppl(
            PplForecastPredictResultSplitWithTaskVO onePredict,
            PplForecastTransformRecord pplforecasttransformrecord,
            PplVersionData pplInfo,
            InsertResult insertResult
    ) {

        List<PplVersionGroupRecordItemDO> batchInsertItem = insertResult.batchInsertItem2insert;
        List<PplOrderDO> order = insertResult.orders2insert;

        PplOrderDO pplOrder = createOrder(pplforecasttransformrecord, onePredict, BigDecimal.ZERO, 0);
        order.add(pplOrder);

        // 注意这里不可以随便换名字，会根据名字来删除数据的
        String sourceType = onePredict.getTaskDO().getSourceType();
        String customerShortName = Ppl13weekForecastSourceTypeEnum.getVersionItemCustomerShortName(sourceType);
        pplOrder.setCustomerShortName(customerShortName);

        PplVersionGroupRecordItemDO groupRecordItemDO = transFromForecast(onePredict);
        setFromPplInfo(groupRecordItemDO, pplInfo);

        groupRecordItemDO.setPplOrder(pplOrder.getPplOrder());
        groupRecordItemDO.setCreator(LoginUtils.getUserNameWithSystem());

        groupRecordItemDO.setForecastModelDetailId(onePredict.getId());
        groupRecordItemDO.setPplId(pplOrder.getPplOrder() + "-001");
        groupRecordItemDO.setAlternativeInstanceType("");
        groupRecordItemDO.setIsSpike(0);

        groupRecordItemDO.setNote("CBS模型预测下发");
        groupRecordItemDO.setInstanceType("");
        groupRecordItemDO.setTotalCore(0);
        groupRecordItemDO.setInstanceNum(0);
        groupRecordItemDO.setDataDiskNum(1);

        if (Lang.list("SSD", "其它").contains(onePredict.getGinsFamily())) {
            groupRecordItemDO.setDataDiskType(PplDiskTypeEnum.CLOUD_SSD.getName());
        }else {
            groupRecordItemDO.setDataDiskType(PplDiskTypeEnum.CLOUD_PREMIUM.getName());
        }
        groupRecordItemDO.setDataDiskStorage(onePredict.getCoreNum().intValue());
        groupRecordItemDO.setTotalDisk(onePredict.getCoreNum().intValue());



        // 对于每一条下发的数据， 查找一下是否在 ppl 中存在
        batchInsertItem.add(groupRecordItemDO);
    }

    private void setFromPplInfo(PplVersionGroupRecordItemDO itemDO, PplVersionData pplInfo) {
        itemDO.setVersionGroupId(pplInfo.versionGroupDO.getId());
        itemDO.setVersionGroupRecordId(pplInfo.versionGroupRecordDO.getId());
        itemDO.setRecordVersion(pplInfo.versionGroupRecordDO.getRecordVersion());

    }


    private void trans2ppl(PplForecastPredictResultSplitWithTaskVO onePredict,
            PplForecastTransformRecord pplforecasttransformrecord,
            PplVersionData pplRecord,
            InsertResult insertResult,
            PplForecastTransformToPplDO toPplRecord) {

        PplVersionGroupDO versionGroupDO = pplRecord.versionGroupDO;
        PplVersionGroupRecordDO versionGroupRecordDO = pplRecord.versionGroupRecordDO;

        List<PplVersionGroupRecordItemDO> batchInsertItem = insertResult.batchInsertItem2insert;
        List<PplOrderDO> order = insertResult.orders2insert;

        String ginsFamily = onePredict.getGinsFamily();
        Map<String, InstanceTypeInfo> instanceTypeInfoMap = ppl13weekPredictService.getInstanceTypeInfo();
        InstanceTypeInfo instanceTypeInfo = instanceTypeInfoMap.get(ginsFamily);

        Integer totalCoreNum = onePredict.getCoreNum().intValue();
        String instanceModel = onePredict.getInstanceModel();

        Integer coreNum = onePredict.getInstanceModelCpu();
        Integer totalCvmNum = Math.round((float) totalCoreNum / coreNum);
        totalCoreNum = totalCvmNum * coreNum; // 重新取整
        // 大核心逻辑，且不足1台CVM
        if (instanceTypeInfo.getBigCoreCpu() != null
                && instanceTypeInfo.getBigCoreCpu() > 8
                && totalCoreNum.compareTo(instanceTypeInfo.getBigCoreCpu()) < 0) {
            instanceModel = instanceTypeInfo.getSmallCoreInstanceModel();
            if (Strings.isNotBlank(instanceModel)) {
                coreNum = instanceTypeInfo.getSmallCoreCpu();
                totalCvmNum = Math.round((float) totalCoreNum / coreNum);
                totalCoreNum = totalCvmNum * coreNum;
                Optional.ofNullable(toPplRecord).ifPresent(o -> o.appendNote("大核心不足1台，转为小核心"));
            }
        }

        if (toPplRecord != null) {
            toPplRecord.addTransformNum(BigDecimal.valueOf(totalCoreNum));
            toPplRecord.appendNote("核心数:%d,总核心数:%d,实例数=总/单核心数:%d", coreNum, totalCoreNum, totalCvmNum);
        }

        // 小于1 台的cvm 不下发
        if (totalCvmNum < 1) {
            Optional.ofNullable(toPplRecord).ifPresent(o -> o.appendNote("实例不足1台，不下发"));
            return;
        }

        PplOrderDO pplOrder = createOrder(pplforecasttransformrecord, onePredict, BigDecimal.ZERO, totalCoreNum);
        order.add(pplOrder);

        // 注意这里不可以随便换名字，会根据名字来删除数据的
        String sourceType = onePredict.getTaskDO().getSourceType();
        String customerShortName = Ppl13weekForecastSourceTypeEnum.getVersionItemCustomerShortName(sourceType);
        pplOrder.setCustomerShortName(customerShortName);

        PplVersionGroupRecordItemDO groupRecordItemDO = transFromForecast(onePredict);
        groupRecordItemDO.setInstanceModel(instanceModel);

        groupRecordItemDO.setTotalCore(totalCoreNum);
        groupRecordItemDO.setInstanceNum(totalCvmNum);
        groupRecordItemDO.setPplOrder(pplOrder.getPplOrder());
        groupRecordItemDO.setVersionGroupId(versionGroupDO.getId());
        groupRecordItemDO.setVersionGroupRecordId(versionGroupRecordDO.getId());
        groupRecordItemDO.setRecordVersion(versionGroupRecordDO.getRecordVersion());
        groupRecordItemDO.setCreator(LoginUtils.getUserNameWithSystem());

        groupRecordItemDO.setForecastModelDetailId(onePredict.getId());

        // groupRecordItemDO.setPplId(pplCommonService.generatePplItemId(pplOrder.getPplOrder()));
        // 目前中长尾一个ppl order只会有1条ppl-id，因此写死ppl id为001
        groupRecordItemDO.setPplId(pplOrder.getPplOrder() + "-001");

        groupRecordItemDO.setTotalCore(totalCoreNum);
        groupRecordItemDO.setInstanceNum(totalCvmNum);
        groupRecordItemDO.setAlternativeInstanceType("");

        groupRecordItemDO.setIsSpike(0);
        groupRecordItemDO.setNote("");

        // 对于每一条下发的数据， 查找一下是否在 ppl 中存在

        batchInsertItem.add(groupRecordItemDO);
    }

    private static void deleteOldDataBeforeInsert(
            QueryPpl13weekTransformReq req,
            List<PplVersionGroupRecordItemDO> batchInsertItem,
            PplVersionData pplInfo, List<String> customerShortNames) {

        PplForecastPredictTaskOutputVersionDO splitVersion = pplInfo.getSplitVersions().get(0);
        boolean isCvm = !SPLIT_CBS.getCode().equals(splitVersion.getOutputVersionType());

        PplVersionGroupRecordDO versionGroupRecordDO = pplInfo.versionGroupRecordDO;
        // 插入前，首先处理覆盖删除
        if (!req.getIsAppend()) {

            LocalDate minBeginBuyDate;
            LocalDate maxBeginBuyDate;
            if (!batchInsertItem.isEmpty()) {
                minBeginBuyDate = batchInsertItem.get(0).getBeginBuyDate();
                maxBeginBuyDate = batchInsertItem.get(0).getBeginBuyDate();
                for (PplVersionGroupRecordItemDO item : batchInsertItem) {
                    LocalDate currentBeginBuyDate = item.getBeginBuyDate();
                    if (currentBeginBuyDate.isBefore(minBeginBuyDate)) {
                        minBeginBuyDate = currentBeginBuyDate;
                    }
                    if (currentBeginBuyDate.isAfter(maxBeginBuyDate)) {
                        maxBeginBuyDate = currentBeginBuyDate;
                    }
                }
                minBeginBuyDate = minBeginBuyDate.withDayOfMonth(1);
                maxBeginBuyDate = maxBeginBuyDate.withDayOfMonth(maxBeginBuyDate.lengthOfMonth());
            }
            // 这里做一个特殊逻辑，如果下发的没有数据，就清空选中的数据
            else {
                // 将startYearMonth和endYearMonth转换成日期对象
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                minBeginBuyDate = YearMonth.parse(req.getStartYearMonth(), formatter).atDay(1);
                maxBeginBuyDate = YearMonth.parse(req.getEndYearMonth(), formatter).atEndOfMonth();
            }

            customerShortNames.addAll(Lang.list("中长尾模型预测", "模型预测"));

            // 上面已经自动通过了， 这里是第二个版本了
            // 这里删除再加上一个 record 的限制
            String deleteSql = "where version_group_record_id=? and ppl_order in (select ppl_order\n" +
                    "                    from ppl_order\n" +
                    "                    where customer_short_name in (?) and source = 'FORECAST' and deleted=0) "
                    + "and begin_buy_date >= ? and begin_buy_date <= ? and deleted=0 ";
            if (isCvm) {
                deleteSql += " and (data_disk_type = '' or data_disk_type is null)";
            }else {
                deleteSql += " and data_disk_type != ''";
            }
            DBList.demandDBHelper.delete(PplVersionGroupRecordItemDO.class, deleteSql, versionGroupRecordDO.getId(),
                    customerShortNames, minBeginBuyDate, maxBeginBuyDate);
        }
    }

    @NotNull
    private PplVersionGroupRecordDO approveIfVersionInStartStatus(PplVersionData transToPplVersionData) {
        PplVersionGroupRecordDO versionGroupRecordDO = transToPplVersionData.versionGroupRecordDO;
        PplVersionGroupDO versionGroupDO = transToPplVersionData.versionGroupDO;
        //   按时间继承，如果create 状态，直接先通过一下
        if (Lang.list(PplVersionGroupStatusEnum.CREATE.getCode(), PplVersionGroupStatusEnum.PRE_SUBMIT.getCode())
                .contains(versionGroupRecordDO.getStatus())) {
            ApproveVersionGroupReq data = new ApproveVersionGroupReq();
            data.setApproveNote("插入13周数据，自动通过");
            data.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
            data.setGroupId(versionGroupDO.getId());
            data.setSystem(true);
            data.setCurrentStatus(versionGroupRecordDO.getStatus());
            pplVersionGroupService.approveVersionGroup(data);

            // 通过之后重新取一下 record，要重新取
            versionGroupRecordDO = getVersionGroupRecord(versionGroupDO);
        }
        return versionGroupRecordDO;
    }

    private List<PplForecastPredictResultSplitWithTaskVO> newTailFilterAndRecord(QueryPpl13weekTransformReq req,
            PplForecastTransformRecord pplforecasttransformrecord,
            List<PplForecastPredictResultSplitWithTaskVO> allDetail) {

        int total = allDetail.size();
        // 将startYearMonth和endYearMonth转换成日期对象
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate startDate = YearMonth.parse(req.getStartYearMonth(), formatter).atDay(1);
        LocalDate endDate = YearMonth.parse(req.getEndYearMonth(), formatter).atEndOfMonth();

        int zeroFiltered = (int) allDetail.stream()
                .filter(vo -> (vo.getCvmNum() == null || vo.getCvmNum() == 0)).count();
        int dateFiltered = (int) allDetail.stream().filter(vo -> {
            LocalDate recordDate = vo.getStatTime();
            return !((recordDate.isEqual(startDate) || recordDate.isAfter(startDate))
                    && (recordDate.isEqual(endDate) || recordDate.isBefore(endDate)));
        }).count();

        // 生成统计信息到desc中
        allDetail = allDetail.stream()
                .filter(vo -> !(vo.getCvmNum() == null || vo.getCvmNum() == 0))
                .filter(vo -> {
                    // 判断时间范围是否包含记录的时间
                    LocalDate recordDate = vo.getStatTime();
                    return (recordDate.isEqual(startDate) || recordDate.isAfter(startDate))
                            && (recordDate.isEqual(endDate) || recordDate.isBefore(endDate));
                })
                .collect(Collectors.toList());

        String desc = String.format(
                "共查询到%d条记录，其中cvm=0的过滤掉%d条记录，可用区过滤掉%d条记录，机型过滤掉%d条记录，时间范围过滤掉%d条记录,剩余%d条记，剩余%d。",
                total, zeroFiltered, 0, 0, dateFiltered, allDetail.size(), allDetail.size());
        String d = pplforecasttransformrecord.getDesc() == null ? "" : pplforecasttransformrecord.getDesc();
        d += "\n";
        pplforecasttransformrecord.setDesc(d + desc);
        return allDetail;
    }

    private List<PplForecastPredictResultSplitWithTaskVO> filterAndRecord(
            QueryPpl13weekTransformReq req, PplForecastTransformRecord pplforecasttransformrecord,
            List<PplForecastPredictResultSplitWithTaskVO> allDetail) {
        // 将startYearMonth和endYearMonth转换成日期对象
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate startDate = YearMonth.parse(req.getStartYearMonth(), formatter).atDay(1);
        LocalDate endDate = YearMonth.parse(req.getEndYearMonth(), formatter).atEndOfMonth();

        ConfigWhiteList configWhiteList = getConfigWhiteList();

// 过滤记录，并统计不同条件过滤掉的记录数
        int total = allDetail.size();
        int azFiltered = (int) allDetail.stream()
                .filter(vo -> !(isNotFilterConfig() ||
                        (configWhiteList.getAzList().contains(vo.getZoneName())
                                || configWhiteList.getRegionList().contains(vo.getRegionName())))).count();
        int modelFiltered = (int) allDetail.stream()
                .filter(vo -> !(isNotFilterConfig() ||
                        (configWhiteList.getModelList().contains(vo.getGinsFamily())))).count();
        int zeroFiltered = (int) allDetail.stream()
                .filter(vo -> (vo.getCvmNum() == null || vo.getCvmNum() == 0)).count();
        int dateFiltered = (int) allDetail.stream().filter(vo -> {
            LocalDate recordDate = vo.getStatTime();
            return !((recordDate.isEqual(startDate) || recordDate.isAfter(startDate))
                    && (recordDate.isEqual(endDate) || recordDate.isBefore(endDate)));
        }).count();

// 生成统计信息到desc中
        allDetail = allDetail.stream()
                .filter(vo -> isNotFilterConfig() ||
                        (configWhiteList.getAzList().contains(vo.getZoneName())
                                || configWhiteList.getRegionList().contains(vo.getRegionName())))
                .filter(vo -> isNotFilterConfig() ||
                        (configWhiteList.getModelList().contains(vo.getGinsFamily())))
                .filter(vo -> !(vo.getCvmNum() == null || vo.getCvmNum() == 0))
                .filter(vo -> {
                    // 判断时间范围是否包含记录的时间
                    LocalDate recordDate = vo.getStatTime();
                    return (recordDate.isEqual(startDate) || recordDate.isAfter(startDate))
                            && (recordDate.isEqual(endDate) || recordDate.isBefore(endDate));
                })
                .collect(Collectors.toList());

        // 首先，将数据按照需要匹配的字段进行分组
        Map<String, List<PplForecastPredictResultSplitWithTaskVO>> groupedData = allDetail.stream()
                .collect(Collectors.groupingBy(
                        vo -> vo.getStatTime() + vo.getBillType() + vo.getPredictIndex() + vo.getGinsFamily()
                                + vo.getRegionName() + vo.getZoneName() + vo.getCoreType() + vo.getHistoryNum()
                                + vo.getInstanceModel()));

        // 然后，在每个组内部进行计算
        List<PplForecastPredictResultSplitWithTaskVO> result = new ArrayList<>();
        for (List<PplForecastPredictResultSplitWithTaskVO> group : groupedData.values()) {
            BigDecimal newCvmNum = BigDecimal.ZERO;
            BigDecimal newCoreNum = BigDecimal.ZERO;
            BigDecimal retCvmNum = BigDecimal.ZERO;
            BigDecimal retCoreNum = BigDecimal.ZERO;

            for (PplForecastPredictResultSplitWithTaskVO vo : group) {
                if (PplForecastTypeEnum.NEW.getType().equals(vo.getForecastSeqType())) {
                    newCvmNum = newCvmNum.add(BigDecimal.valueOf(vo.getCvmNum()));
                    newCoreNum = newCoreNum.add(vo.getCoreNum());
                } else if (PplForecastTypeEnum.RET.getType().equals(vo.getForecastSeqType())) {
                    retCvmNum = retCvmNum.add(BigDecimal.valueOf(vo.getCvmNum()));
                    retCoreNum = retCoreNum.add(vo.getCoreNum());
                }
            }

            BigDecimal netCvmNum = newCvmNum.subtract(retCvmNum);
            BigDecimal netCoreNum = newCoreNum.subtract(retCoreNum);
            if (netCvmNum.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            PplForecastPredictResultSplitWithTaskVO netVo = new PplForecastPredictResultSplitWithTaskVO();
            // 这里假设所有的group中的元素都有相同的属性值
            BeanUtils.copyProperties(group.get(0), netVo);
            netVo.setCvmNum(Math.abs(netCvmNum.intValue()));
            netVo.setCoreNum(netCoreNum.abs());

            if (netCvmNum.compareTo(BigDecimal.ZERO) > 0) {
                netVo.setType(PplForecastTypeEnum.NEW.getType());
            } else if (netCvmNum.compareTo(BigDecimal.ZERO) < 0) {
                netVo.setType(PplForecastTypeEnum.RET.getType());
            }
            result.add(netVo);
        }

        String desc = String.format(
                "共查询到%d条记录，其中cvm=0的过滤掉%d条记录，可用区过滤掉%d条记录，机型过滤掉%d条记录，时间范围过滤掉%d条记录,剩余%d条记，净增剩余%d。",
                total, zeroFiltered, azFiltered, modelFiltered, dateFiltered, allDetail.size(), result.size());
        String d = pplforecasttransformrecord.getDesc() == null ? "" : pplforecasttransformrecord.getDesc();
        d += "\n";
        pplforecasttransformrecord.setDesc(d + desc);
        return result;
    }

    private Boolean isNotFilterConfig() {
        return "true".equals(transformConfig.get());
    }

    DynamicProperty<String> transformConfig = DynamicProperty.create("ppl13week.config.transform", "true");


    @NotNull
    private PplVersionGroupRecordDO getVersionGroupRecord(PplVersionGroupDO versionGroupDO) {
        PplVersionGroupRecordDO versionGroupRecordDO = DBList.demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where  id = (select max(id) from ppl_version_group_record where version_group_id = ?)",
                versionGroupDO.getId());
        if (versionGroupRecordDO == null) {
            PplVersionGroupRecordDO recordDO = new PplVersionGroupRecordDO();
            recordDO.setVersionGroupId(versionGroupDO.getId());
            Integer recordVersion = pplCommonService.getVersionGroupRecordVersion();
            recordDO.setRecordVersion(recordVersion);
            recordDO.setStatus(PplVersionGroupStatusEnum.CREATE.getCode());
            DBList.demandDBHelper.insert(recordDO);
            versionGroupRecordDO = recordDO;
        }
        return versionGroupRecordDO;
    }

    @NotNull
    private PplVersionGroupDO getVersionGroup(QueryPpl13weekTransformReq req, String versionGroupProduct) {
        PplVersionGroupDO versionGroupDO = DBList.demandDBHelper.getOne(PplVersionGroupDO.class,
                "where product=? and version_code=? and industry_dept=?",
                versionGroupProduct, req.getVersionCode(), "中长尾");
        if (versionGroupDO == null) {
            PplVersionGroupDO groupDO = new PplVersionGroupDO();
            groupDO.setVersionCode(req.getVersionCode());
            groupDO.setIndustryDept("中长尾");
            groupDO.setProduct(versionGroupProduct);
            groupDO.setStatus(PplVersionGroupStatusEnum.CREATE.getCode());
            DBList.demandDBHelper.insert(groupDO);
            versionGroupDO = groupDO;
        }
        return versionGroupDO;
    }

    private void setRecord(QueryPpl13weekTransformReq req, String uuid,
            PplForecastTransformRecord pplforecasttransformrecord) {
        pplforecasttransformrecord.setCreateUser(LoginUtils.getUserNameWithSystem());
        pplforecasttransformrecord.setBatchUuid(uuid);
        pplforecasttransformrecord.setIsAppend(req.getIsAppend());
        pplforecasttransformrecord.setStartYearMonth(DateUtils.parseLocalDate(req.getStartYearMonth()));
        pplforecasttransformrecord.setEndYearMonth(DateUtils.parseLocalDate(req.getEndYearMonth()));
        pplforecasttransformrecord.setIsLimitByDate(req.getIsLimitByDate());
        pplforecasttransformrecord.setVersionCode(req.getVersionCode());
        pplforecasttransformrecord.setStatus("FINISH");
        pplforecasttransformrecord.setDesc(req.getDesc());
    }


    @Override
    public Map<Tuple2<Integer, Integer>, List<PplForecastConfigHolidayWeekDO>> getWeekData() {
        List<PplForecastConfigHolidayWeekDO> all = DBList.demandDBHelper.getAll(PplForecastConfigHolidayWeekDO.class);
        return ListUtils.groupBy(all,
                (o) -> Tuple.of(o.getYear(), o.getMonth()));
    }

    @Override
    public void updateWeekData() {

        Map<Tuple2<Integer, Integer>, List<PplForecastConfigHolidayWeekDO>> tuple2ListMap = getWeekData();
        for (Tuple2<Integer, Integer> key : tuple2ListMap.keySet()) {
            List<PplForecastConfigHolidayWeekDO> one = tuple2ListMap.get(key);
            ListUtils.sortAscNullLast(one, PplForecastConfigHolidayWeekDO::getStart);
            boolean isAllNull = true;
            for (PplForecastConfigHolidayWeekDO pplForecastConfigHolidayWeekDO : one) {
                if (pplForecastConfigHolidayWeekDO.getRate() != null && pplForecastConfigHolidayWeekDO.getRate() >= 0) {
                    isAllNull = false;
                    break;
                }
            }
            if (isAllNull) {
                if (one.size() == 4) {
                    one.get(0).setRate(2);
                    one.get(1).setRate(3);
                    one.get(2).setRate(3);
                    one.get(3).setRate(2);
                    DBList.demandDBHelper.update(one);
                }
                if (one.size() == 5) {
                    one.get(0).setRate(1);
                    one.get(1).setRate(2);
                    one.get(2).setRate(2);
                    one.get(3).setRate(3);
                    one.get(4).setRate(2);
                    DBList.demandDBHelper.update(one);
                }
            }
        }
    }

    private PplOrderDO createOrder(PplForecastTransformRecord req, PplForecastPredictResultSplitWithTaskVO onePredict,
            BigDecimal totalDiskNum, Integer totalCoreNum) {
        PplOrderDO pplOrderDO = new PplOrderDO();
        pplOrderDO.setIndustry("中长尾");
        pplOrderDO.setWarZone("");
        pplOrderDO.setCustomerType(CustomerTypeEnum.EXISTING.getCode());
        pplOrderDO.setCustomerUin("");
        pplOrderDO.setCustomerName(
                "模型预测-" + onePredict.getTaskDO().getPredictAlgorithm() + "@" + onePredict.getTaskDO()
                        .getPredictAlgorithmArgs());
        pplOrderDO.setCustomerShortName("模型预测");
        pplOrderDO.setCustomerSource("");
        pplOrderDO.setIndustryDept("中长尾");
        pplOrderDO.setSubmitUser(LoginUtils.getUserNameWithSystem());
        pplOrderDO.setAllCore(totalCoreNum);
        pplOrderDO.setAllDisk(totalDiskNum.intValue());

        pplOrderDO.setForecastResultId(onePredict.getId());
        pplOrderDO.setForecastSourceUuid(Strings.join("-", req.getBatchUuid(), req.getOutputVersionId()));
        pplOrderDO.setSource(PplOrderSourceTypeEnum.FORECAST.getCode());

        //String pplOrderId = pplCommonService.generatePplOrderId("N");

        synchronized (pplOrderPool) {
            String pplOrder = "";
            if (pplOrderPool.isEmpty()) {
                pplOrderPool.addAll(pplCommonService.generatePplOrderId("N", 100));
                pplOrder = pplOrderPool.remove(0);
            } else {
                pplOrder = pplOrderPool.remove(0);
                if (!pplOrder.startsWith("PN" + DateUtils.format(new Date(), "yyMMdd"))) { // 非当天
                    pplOrderPool.clear();
                    pplOrderPool.addAll(pplCommonService.generatePplOrderId("N", 100));
                    pplOrder = pplOrderPool.remove(0);
                }
            }
            pplOrderDO.setPplOrder(pplOrder);
        }

        pplOrderDO.setStatus(PplOrderStatusEnum.PRE_SUBMIT.getCode());
        //pplOrderDO.setPplOrder(pplOrderId);
        return pplOrderDO;
    }

    private PplVersionGroupRecordItemDO transFromForecast(PplForecastPredictResultSplitWithTaskVO detail) {
        PplVersionGroupRecordItemDO item = new PplVersionGroupRecordItemDO();

        item.setForecastModelDetailId(detail.getId());

        item.setStatus(PplItemStatusEnum.VALID.getCode());
        item.setProduct(Ppl13weekProductTypeEnum.CVM.getName());

        String demandType = "";
        if (Strings.equals(detail.getForecastSeqType(), PplForecastTypeEnum.NEW.getType())) {
            demandType = PplDemandTypeEnum.NEW.getCode();
        }
        if (Strings.equals(detail.getForecastSeqType(), PplForecastTypeEnum.RET.getType())) {
            demandType = PplDemandTypeEnum.RETURN.getCode();
        }

        item.setDemandType(demandType);
        item.setWinRate(null);

        String nameByCode = Ppl13weekForecastBillTypeEnum.getNameByCode(detail.getBillType());
        item.setBillType(nameByCode);

        item.setBeginBuyDate(detail.getBeginBuyDate());
        item.setEndBuyDate(detail.getEndBuyDate());

        item.setInstanceModel("");
//        item.setImportType("system");
        item.setRegionName(detail.getRegionName());
        item.setZoneName(detail.getZoneName());
        item.setInstanceType(detail.getGinsFamily());
        item.setInstanceModel(detail.getInstanceModel());
        return item;
    }
}
